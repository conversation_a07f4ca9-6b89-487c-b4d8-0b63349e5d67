"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:5000/api\";\nclass ApiClient {\n    async request(endpoint, options) {\n        const url = \"\".concat(API_BASE_URL).concat(endpoint);\n        const response = await fetch(url, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options === null || options === void 0 ? void 0 : options.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            throw new Error(\"API request failed: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n    // Companies\n    async getCompanies() {\n        return this.request(\"/company\");\n    }\n    // Blog workflow\n    async startBlog(companyId, userId) {\n        return this.request(\"/blog/start\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                companyId,\n                userId\n            })\n        });\n    }\n    async selectKeywordAnalyze(draftId, selectedKeyword, alternativeKeywords) {\n        return this.request(\"/blog/select-keyword-analyze\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                selectedKeyword,\n                alternativeKeywords\n            })\n        });\n    }\n    async generateMetaScores(draftId) {\n        return this.request(\"/blog/generate-meta-scores\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    async selectMeta(draftId, selectedMetaIndex) {\n        return this.request(\"/blog/select-meta\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                selectedMetaIndex\n            })\n        });\n    }\n    async generateStructuredContent(draftId) {\n        return this.request(\"/blog/generate-structured-content\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    async regenerateBlock(draftId, blockId, regenerationType, customPrompt, newContent) {\n        return this.request(\"/blog/regenerate-block\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                blockId,\n                regenerationType,\n                customPrompt,\n                newContent\n            })\n        });\n    }\n    async generateLinks(draftId) {\n        return this.request(\"/blog/generate-links\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    async deployWordPress(draftId, wordpressConfig) {\n        return this.request(\"/blog/deploy-wordpress\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                wordpressConfig\n            })\n        });\n    }\n    // Draft management\n    async getDraft(draftId) {\n        return this.request(\"/blog/draft/\".concat(draftId));\n    }\n    async listDrafts(userId) {\n        const params = userId ? \"?userId=\".concat(userId) : \"\";\n        return this.request(\"/blog/drafts\".concat(params));\n    }\n    // WordPress\n    async testWordPress() {\n        return this.request(\"/blog/test-wordpress\", {\n            method: \"POST\"\n        });\n    }\n    async previewWordPress(draftId) {\n        return this.request(\"/blog/preview-wordpress\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    // Draft management - additional methods\n    async deleteDraft(draftId) {\n        return this.request(\"/blog/draft/\".concat(draftId), {\n            method: \"DELETE\"\n        });\n    }\n    async saveDraft(draftId, updates) {\n        return this.request(\"/blog/save-draft\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                updates\n            })\n        });\n    }\n    // WordPress deployment\n    async deployToWordPress(draftId, credentials) {\n        return this.request(\"/blog/\".concat(draftId, \"/deploy\"), {\n            method: \"POST\",\n            body: JSON.stringify(credentials)\n        });\n    }\n}\nconst api = new ApiClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});