// Blog Workflow Controller - Handles the main blog generation workflow
const draftService = require('../services/draft.service');
const googleSheetsService = require('../services/googleSheets.service');
const competitorService = require('../services/competitor.service');
const geminiService = require('../services/gemini.service');

class BlogWorkflowController {
  // Start blog generation workflow
  async startBlog(req, res) {
    try {
      const { companyId, userId } = req.body;
      
      if (!companyId) {
        return res.status(400).json({ error: 'Company ID is required' });
      }

      // Get company data from Google Sheets
      const companyData = await googleSheetsService.getCompanyById(companyId);
      if (!companyData) {
        return res.status(404).json({ error: 'Company not found' });
      }

      // Get keyword suggestions for this company
      const keywordSuggestions = await googleSheetsService.getKeywordSuggestions(companyId);

      // Create initial draft
      const draft = await draftService.createDraft({
        userId: userId || 'anonymous',
        companyData,
        keywordSuggestions,
        status: 'keyword_selection'
      });

      res.json({
        success: true,
        draftId: draft.id,
        companyData,
        keywordSuggestions,
        message: 'Blog generation started successfully'
      });

    } catch (error) {
      console.error('Error starting blog:', error);
      res.status(500).json({ error: 'Failed to start blog generation' });
    }
  }

  // Select keyword and perform analysis
  async selectKeywordAndAnalyze(req, res) {
    try {
      const { draftId, selectedKeyword, alternativeKeywords } = req.body;
      
      if (!draftId || !selectedKeyword) {
        return res.status(400).json({ error: 'Draft ID and selected keyword are required' });
      }

      // Get draft
      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      // Save alternative keywords as drafts
      if (alternativeKeywords && alternativeKeywords.length > 0) {
        await draftService.saveAlternativeKeywords(draftId, alternativeKeywords);
      }

      // Perform competitor analysis
      const competitorAnalysis = await competitorService.analyzeCompetitors(selectedKeyword);
      
      // Generate keyword cluster and trends
      const keywordCluster = await geminiService.generateKeywordCluster(selectedKeyword);
      const trends = await geminiService.getCurrentTrends(selectedKeyword);

      // Update draft with analysis
      await draftService.updateDraft(draftId, {
        selectedKeyword,
        competitorAnalysis,
        keywordCluster,
        trends,
        status: 'meta_generation'
      });

      res.json({
        success: true,
        competitorAnalysis,
        keywordCluster,
        trends,
        message: 'Keyword analysis completed successfully'
      });

    } catch (error) {
      console.error('Error in keyword analysis:', error);
      res.status(500).json({ error: 'Failed to analyze keyword' });
    }
  }
}

module.exports = new BlogWorkflowController();
