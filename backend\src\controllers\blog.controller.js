class MetaService {
  async generateScoredMetaOptions(keyword, keywordCluster, companyData, trends) {
    const { model } = require('../config/gemini.config');
    const currentYear = new Date().getFullYear();
    
    const prompt = `
      Generate 3 SEO-optimized H1 + Meta Title + Meta Description options for:
      Primary Keyword: ${keyword}
      Secondary Keywords: ${keywordCluster.secondaryKeywords.map(k => k.keyword).join(', ')}
      LSI Keywords: ${keywordCluster.lsiKeywords.join(', ')}
      Company: ${companyData.companyName}
      Current Trends: ${trends.currentTrends.join(', ')}
      Year: ${currentYear}
      
      For each option, calculate SEO scores based on:
      - Keyword inclusion (40 points)
      - Length optimization (20 points)
      - Readability (20 points)
      - Trend relevance (20 points)
      
      Return in JSON format:
      {
        "options": [
          {
            "h1Title": "title here",
            "metaTitle": "meta title here",
            "metaDescription": "description here",
            "scores": {
              "keywordScore": 0-40,
              "lengthScore": 0-20,
              "readabilityScore": 0-20,
              "trendScore": 0-20,
              "totalScore": 0-100
            },
            "keywordsIncluded": ["keyword1", "keyword2"]
          }
        ]
      }
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      const cleanedText = text.replace(/```json
\n?/g, '').replace(/
```\n?/g, '').trim();
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return parsed.options;
      }
    } catch (parseError) {
      console.error('Meta options parsing error:', parseError);
    }

    // Fallback meta options with scores
    return [
      {
        h1Title: `${keyword} - Complete Guide ${currentYear}`,
        metaTitle: `${keyword} Guide ${currentYear} | ${companyData.companyName}`,
        metaDescription: `Master ${keyword} with our expert guide. Learn best practices, cost analysis, and implementation strategies from ${companyData.companyName} professionals.`,
        scores: {
          keywordScore: 35,
          lengthScore: 18,
          readabilityScore: 17,
          trendScore: 15,
          totalScore: 85
        },
        keywordsIncluded: [keyword, "guide", currentYear.toString()]
      },
      {
        h1Title: `Professional ${keyword} Services & Solutions`,
        metaTitle: `${keyword} Services | Expert Solar Solutions`,
        metaDescription: `Professional ${keyword} services by ${companyData.companyName}. Trusted by installers nationwide for reliable, efficient solar solutions and expert support.`,
        scores: {
          keywordScore: 38,
          lengthScore: 19,
          readabilityScore: 18,
          trendScore: 17,
          totalScore: 92
        },
        keywordsIncluded: [keyword, "services", "solutions", "professional"]
      },
      {
        h1Title: `${keyword}: Essential Tips & Best Practices`,
        metaTitle: `${keyword} Tips & Best Practices ${currentYear}`,
        metaDescription: `Discover essential ${keyword} tips and industry best practices. ${companyData.companyName} shares proven strategies for successful solar projects in ${currentYear}.`,
        scores: {
          keywordScore: 36,
          lengthScore: 17,
          readabilityScore: 19,
          trendScore: 16,
          totalScore: 88
        },
        keywordsIncluded: [keyword, "tips", "best practices", currentYear.toString()]
      }
    ];
  }

  async generateMetaOptions(keyword, companyData, competitorAnalysis) {
    // For now, use the existing method with mock data
    const mockKeywordCluster = {
      secondaryKeywords: [
        { keyword: 'solar installation', relevance: 0.8 },
        { keyword: 'solar design', relevance: 0.7 }
      ],
      lsiKeywords: ['solar panels', 'renewable energy', 'solar system']
    };

    const mockTrends = {
      currentTrends: ['sustainability', 'clean energy', 'cost savings']
    };

    return await this.generateScoredMetaOptions(keyword, mockKeywordCluster, companyData, mockTrends);
  }

  async regenerateMetaOptions(keyword, companyData, competitorAnalysis, optionIndex) {
    // Generate new meta options
    const newOptions = await this.generateMetaOptions(keyword, companyData, competitorAnalysis);

    // If optionIndex is provided, return only that option, otherwise return all
    if (optionIndex !== undefined) {
      return [newOptions[0]]; // Return the first new option to replace the specified index
    }

    return newOptions;
  }
}

module.exports = new MetaService();


  // Generate meta titles, descriptions with SEO scores
  async generateMetaWithScores(req, res) {
    try {
      const { draftId } = req.body;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      // Generate 3 meta options with SEO scores
      const metaOptions = await metaService.generateMetaOptions(
        draft.selectedKeyword,
        draft.companyData,
        draft.competitorAnalysis
      );

      // Update draft
      await draftService.updateDraft(draftId, {
        metaOptions,
        status: 'meta_selection'
      });

      res.json({
        success: true,
        metaOptions,
        message: 'Meta options generated successfully'
      });

    } catch (error) {
      console.error('Error generating meta:', error);
      res.status(500).json({ error: 'Failed to generate meta options' });
    }
  }

  // Select final meta and proceed to content generation
  async selectMeta(req, res) {
    try {
      const { draftId, selectedMetaIndex } = req.body;
      
      if (!draftId || selectedMetaIndex === undefined) {
        return res.status(400).json({ error: 'Draft ID and selected meta index are required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      const selectedMeta = draft.metaOptions[selectedMetaIndex];
      if (!selectedMeta) {
        return res.status(400).json({ error: 'Invalid meta option selected' });
      }

      // Update draft with final meta
      await draftService.updateDraft(draftId, {
        finalMeta: selectedMeta,
        status: 'content_generation'
      });

      res.json({
        success: true,
        finalMeta: selectedMeta,
        message: 'Meta selected successfully'
      });

    } catch (error) {
      console.error('Error selecting meta:', error);
      res.status(500).json({ error: 'Failed to select meta' });
    }
  }

  // Generate structured blog content
  async generateStructuredContent(req, res) {
    try {
      const { draftId } = req.body;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      // Generate structured content using content service
      const structuredContent = await contentService.generateWordPressFormattedContent(
        draft.finalMeta,
        draft.selectedKeyword,
        draft.keywordCluster,
        draft.companyData,
        draft.competitorAnalysis,
        draft.trends
      );

      // Convert to blog blocks format
      const blogBlocks = await blockService.convertToBlocks(structuredContent);

      // Update draft with content
      await draftService.updateDraft(draftId, {
        structuredContent,
        blogBlocks,
        status: 'content_review'
      });

      res.json({
        success: true,
        blogBlocks,
        message: 'Blog content generated successfully'
      });

    } catch (error) {
      console.error('Error generating content:', error);
      res.status(500).json({ error: 'Failed to generate blog content' });
    }
  }

  // Regenerate specific block
  async regenerateBlock(req, res) {
    try {
      const { draftId, blockIndex, blockType } = req.body;
      
      if (!draftId || blockIndex === undefined || !blockType) {
        return res.status(400).json({ error: 'Draft ID, block index, and block type are required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      // Regenerate specific block
      const newBlock = await blockService.regenerateBlock(
        blockType,
        blockIndex,
        draft.selectedKeyword,
        draft.companyData,
        draft.blogBlocks
      );

      // Update the specific block in draft
      const updatedBlocks = [...draft.blogBlocks];
      updatedBlocks[blockIndex] = newBlock;

      await draftService.updateDraft(draftId, {
        blogBlocks: updatedBlocks
      });

      res.json({
        success: true,
        newBlock,
        blockIndex,
        message: 'Block regenerated successfully'
      });

    } catch (error) {
      console.error('Error regenerating block:', error);
      res.status(500).json({ error: 'Failed to regenerate block' });
    }
  }

  // Generate internal and external links
  async generateLinks(req, res) {
    try {
      const { draftId } = req.body;

      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      // Generate links using content service
      const links = await contentService.generateInboundOutboundLinks(
        draft.selectedKeyword,
        draft.blogBlocks,
        draft.companyData
      );

      // Update draft with links
      await draftService.updateDraft(draftId, {
        links,
        status: 'ready_to_publish'
      });

      res.json({
        success: true,
        links,
        message: 'Links generated successfully'
      });

    } catch (error) {
      console.error('Error generating links:', error);
      res.status(500).json({ error: 'Failed to generate links' });
    }
  }

  // Deploy blog to WordPress
  async deployToWordPress(req, res) {
    try {
      const { draftId, wordpressConfig } = req.body;

      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      // Convert to WordPress HTML format
      const wordpressHTML = contentService.convertToWordPressHTML(draft);

      // Deploy to WordPress
      const deployResult = await wordpressService.deployPost({
        title: draft.finalMeta.h1Title,
        content: wordpressHTML,
        metaDescription: draft.finalMeta.metaDescription,
        metaTitle: draft.finalMeta.metaTitle,
        featuredImage: draft.structuredContent.featureImage,
        config: wordpressConfig
      });

      // Update draft status
      await draftService.updateDraft(draftId, {
        wordpressPostId: deployResult.postId,
        wordpressUrl: deployResult.url,
        status: 'published'
      });

      res.json({
        success: true,
        postId: deployResult.postId,
        url: deployResult.url,
        message: 'Blog published to WordPress successfully'
      });

    } catch (error) {
      console.error('Error deploying to WordPress:', error);
      res.status(500).json({ error: 'Failed to deploy to WordPress' });
    }
  }

  // Get draft by ID
  async getDraft(req, res) {
    try {
      const { draftId } = req.params;

      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      res.json({
        success: true,
        draft
      });

    } catch (error) {
      console.error('Error getting draft:', error);
      res.status(500).json({ error: 'Failed to get draft' });
    }
  }

  // List all drafts for a user
  async listDrafts(req, res) {
    try {
      const { userId } = req.query;

      const drafts = await draftService.listDrafts(userId || 'anonymous');

      res.json({
        success: true,
        drafts
      });

    } catch (error) {
      console.error('Error listing drafts:', error);
      res.status(500).json({ error: 'Failed to list drafts' });
    }
  }

  // Save draft manually
  async saveDraft(req, res) {
    try {
      const { draftId, updates } = req.body;

      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      await draftService.updateDraft(draftId, updates);

      res.json({
        success: true,
        message: 'Draft saved successfully'
      });

    } catch (error) {
      console.error('Error saving draft:', error);
      res.status(500).json({ error: 'Failed to save draft' });
    }
  }

  // Test WordPress connection
  async testWordPressConnection(req, res) {
    try {
      const { wordpressConfig } = req.body;

      const testResult = await wordpressService.testConnection(wordpressConfig);

      res.json({
        success: true,
        connected: testResult.connected,
        message: testResult.message
      });

    } catch (error) {
      console.error('Error testing WordPress connection:', error);
      res.status(500).json({ error: 'Failed to test WordPress connection' });
    }
  }
}

module.exports = new BlogController();
